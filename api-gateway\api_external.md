# API Gateway - External API Documentation

## Overview
API Gateway menyediakan akses terpusat ke semua microservices dalam sistem ATMA (AI-Driven Talent Mapping Assessment). Gateway ini menangani routing, autentikasi, rate limiting, dan proxy ke berbagai services.

**Gateway Information:**
- **Service Name:** api-gateway
- **Port:** 3000
- **Base URL:** `http://localhost:3000/api/`
- **Version:** 1.0.0

## Authentication
Sebagian besar endpoint memerlukan autentikasi JWT token yang diperoleh dari Auth Service.

**Header Required:**
```
Authorization: Bearer <jwt_token>
```

## Rate Limiting
- **General Gateway:** 5000 requests per 15 minutes
- **Auth Endpoints:** 100 requests per 15 minutes
- **Assessment Endpoints:** 100 requests per 15 minutes
- **Admin Endpoints:** 50 requests per 15 minutes
- **Archive Endpoints:** 5000 requests per 15 minutes
- **Chat Endpoints:** 500 requests per 15 minutes

---

## 🔐 Authentication Service Routes (`/api/auth/`)

### Public Endpoints (No Authentication)

#### POST /api/auth/register
Mendaftarkan user baru ke sistem.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "myPassword1",
  "username": "johndoe"
}
```

**Response Success (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "username": "johndoe",
      "user_type": "user",
      "is_active": true,
      "token_balance": 5
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "User registered successfully"
}
```

#### POST /api/auth/login
Login user ke sistem.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "myPassword1"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "username": "johndoe",
      "user_type": "user",
      "is_active": true,
      "token_balance": 5
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "Login successful"
}
```

### Protected Endpoints (Authentication Required)

#### GET /api/auth/profile
Mendapatkan profil user yang sedang login.

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "email": "<EMAIL>",
    "username": "johndoe",
    "user_type": "user",
    "is_active": true,
    "token_balance": 5,
    "created_at": "2024-01-15T10:30:00.000Z"
  }
}
```

#### PUT /api/auth/profile
Update profil user.

**Request Body:**
```json
{
  "username": "newusername",
  "full_name": "John Doe Updated"
}
```

#### POST /api/auth/change-password
Mengubah password user.

**Request Body:**
```json
{
  "currentPassword": "oldPassword1",
  "newPassword": "newPassword1"
}
```

#### POST /api/auth/logout
Logout user dari sistem.

#### GET /api/auth/token-balance
Mendapatkan saldo token user.

#### GET /api/auth/schools
Mendapatkan daftar sekolah.

#### POST /api/auth/schools
Membuat sekolah baru.

**Request Body:**
```json
{
  "name": "SMA Negeri 1 Jakarta",
  "address": "Jl. Sudirman No. 1",
  "city": "Jakarta",
  "province": "DKI Jakarta"
}
```

---

## 🎯 Assessment Service Routes (`/api/assessment/`)

### Assessment Submission

#### POST /api/assessment/submit
Submit assessment data untuk analisis AI.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Idempotency-Key: <unique_key> (optional)
```

**Request Body:**
```json
{
  "assessmentName": "AI-Driven Talent Mapping",
  "riasec": {
    "realistic": 85,
    "investigative": 92,
    "artistic": 78,
    "social": 65,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 88,
    "conscientiousness": 75,
    "extraversion": 82,
    "agreeableness": 90,
    "neuroticism": 45
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 90,
    "judgment": 78
  }
}
```

**Response Success (202):**
```json
{
  "success": true,
  "data": {
    "jobId": "job_550e8400-e29b-41d4-a716-************",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 1
  },
  "message": "Assessment submitted successfully"
}
```

#### GET /api/assessment/status/:jobId
Mengecek status assessment yang sedang diproses.

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "jobId": "job_550e8400-e29b-41d4-a716-************",
    "status": "completed",
    "progress": 100,
    "resultId": "result_550e8400-e29b-41d4-a716-************"
  }
}
```

### Health Endpoints

#### GET /api/assessment/health
Health check assessment service.

#### GET /api/assessment/health/ready
Readiness probe.

#### GET /api/assessment/health/live
Liveness probe.

#### GET /api/assessment/health/queue
Queue status check.

---

## 📁 Archive Service Routes (`/api/archive/`)

### Results Management

#### GET /api/archive/results
Mendapatkan daftar hasil assessment user.

**Query Parameters:**
- `page` (number): Halaman (default: 1)
- `limit` (number): Jumlah per halaman (default: 10)
- `status` (string): Filter status (completed, failed)

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "result_550e8400-e29b-41d4-a716-************",
        "assessment_name": "AI-Driven Talent Mapping",
        "status": "completed",
        "created_at": "2024-01-15T10:30:00.000Z",
        "analysis_summary": {
          "dominant_archetype": "The Innovator",
          "career_recommendations": ["Software Engineer", "Data Scientist"]
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

#### GET /api/archive/results/:resultId
Mendapatkan detail hasil assessment.

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "id": "result_550e8400-e29b-41d4-a716-************",
    "assessment_name": "AI-Driven Talent Mapping",
    "status": "completed",
    "analysis_summary": {
      "dominant_archetype": "The Innovator",
      "personality_insights": "...",
      "career_recommendations": ["Software Engineer", "Data Scientist"],
      "strengths": ["Problem Solving", "Analytical Thinking"],
      "development_areas": ["Communication", "Leadership"]
    },
    "raw_analysis": "...",
    "created_at": "2024-01-15T10:30:00.000Z"
  }
}
```

### Job Tracking

#### GET /api/archive/jobs
Mendapatkan daftar job assessment user.

#### GET /api/archive/jobs/:jobId
Mendapatkan detail job assessment.

### Statistics

#### GET /api/archive/v1/stats
Endpoint statistik terpadu.

**Query Parameters:**
- `type` (string): user, system, demographic, performance
- `scope` (string): overview, detailed, analysis, summary
- `timeRange` (string): "1 day", "7 days", "30 days", "90 days"

---

## 💬 Chatbot Service Routes (`/api/chatbot/`)

### Conversation Management

#### POST /api/chatbot/conversations
Membuat percakapan baru.

**Request Body:**
```json
{
  "title": "Career Guidance Chat",
  "context_type": "career_guidance",
  "context_data": {},
  "metadata": {}
}
```

**Response Success (201):**
```json
{
  "success": true,
  "data": {
    "id": "conv_550e8400-e29b-41d4-a716-************",
    "title": "Career Guidance Chat",
    "context_type": "career_guidance",
    "status": "active",
    "created_at": "2024-01-15T10:30:00.000Z"
  }
}
```

#### GET /api/chatbot/conversations
Mendapatkan daftar percakapan user.

**Query Parameters:**
- `page` (number): Halaman (default: 1)
- `limit` (number): Jumlah per halaman (default: 20)
- `include_archived` (boolean): Sertakan yang diarsipkan (default: false)
- `context_type` (string): Filter berdasarkan tipe konteks

#### GET /api/chatbot/conversations/:id
Mendapatkan detail percakapan.

**Query Parameters:**
- `include_messages` (boolean): Sertakan pesan (default: false)
- `message_limit` (number): Batas jumlah pesan (default: 50)

#### PUT /api/chatbot/conversations/:id
Update percakapan.

#### DELETE /api/chatbot/conversations/:id
Hapus percakapan (soft delete).

### Message Handling

#### POST /api/chatbot/conversations/:conversationId/messages
Mengirim pesan dan mendapatkan balasan AI.

**Request Body:**
```json
{
  "content": "Hello, I need career guidance",
  "content_type": "text",
  "parent_message_id": null
}
```

**Response Success (201):**
```json
{
  "success": true,
  "data": {
    "user_message": {
      "id": "msg_user_123",
      "content": "Hello, I need career guidance",
      "sender_type": "user",
      "created_at": "2024-01-15T10:30:00.000Z"
    },
    "assistant_message": {
      "id": "msg_assistant_124",
      "content": "Hello! I'd be happy to help you with career guidance...",
      "sender_type": "assistant",
      "created_at": "2024-01-15T10:30:01.000Z"
    },
    "usage": {
      "tokens_used": 150,
      "model": "openai/gpt-3.5-turbo"
    }
  }
}
```

#### GET /api/chatbot/conversations/:conversationId/messages
Mendapatkan pesan dalam percakapan.

#### POST /api/chatbot/conversations/:conversationId/messages/:messageId/regenerate
Regenerate balasan AI untuk pesan tertentu.

### Assessment Integration

#### GET /api/chatbot/assessment-ready/:userId
Mengecek apakah assessment user siap untuk integrasi chat.

#### POST /api/chatbot/conversations/from-assessment
Membuat percakapan berdasarkan hasil assessment.

#### GET /api/chatbot/conversations/:conversationId/suggestions
Mendapatkan saran percakapan berdasarkan assessment.

#### POST /api/chatbot/auto-initialize
Auto-initialize percakapan berdasarkan assessment terbaru.

---

## 🔔 Notification Service Routes (`/api/notifications/`)

### WebSocket Connection
Notification service menyediakan real-time notifications melalui WebSocket di `/socket.io/`.

### Health Check

#### GET /api/notifications/health
Health check notification service.

---

## 👨‍💼 Admin Routes (`/api/admin/`)

### Admin Authentication

#### POST /api/admin/login
Login admin ke sistem.

**Request Body:**
```json
{
  "username": "admin",
  "password": "adminPassword1"
}
```

#### POST /api/admin/register
Mendaftarkan admin baru (hanya untuk superadmin).

**Request Body:**
```json
{
  "username": "newadmin",
  "email": "<EMAIL>",
  "password": "adminPassword1",
  "full_name": "Admin Name",
  "user_type": "admin"
}
```

### Admin User Management

#### DELETE /api/archive/admin/users/:userId
Hapus user secara permanen (soft delete) - hanya untuk admin.

#### PUT /api/archive/admin/users/:userId/token-balance
Update token balance user (untuk admin).

---

## 🏥 Health Check Routes

### Global Health

#### GET /api/health
Main health check untuk semua services.

**Response Success (200):**
```json
{
  "success": true,
  "status": "healthy",
  "services": {
    "auth": "healthy",
    "assessment": "healthy",
    "archive": "healthy",
    "notification": "healthy",
    "chatbot": "healthy"
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

#### GET /api/health/metrics
Metrics endpoint.

#### GET /api/health/ready
Readiness probe.

#### GET /api/health/live
Liveness probe.

---

## 📋 Error Response Format

Semua error response menggunakan format standar:

```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "details": {} // Optional additional details
}
```

### Common Error Codes
- `UNAUTHORIZED` - Token tidak valid atau tidak ada
- `FORBIDDEN` - Akses ditolak
- `RATE_LIMIT_EXCEEDED` - Rate limit terlampaui
- `VALIDATION_ERROR` - Data input tidak valid
- `NOT_FOUND` - Resource tidak ditemukan
- `INTERNAL_ERROR` - Server error

---

## 🔒 Security Headers

API Gateway menambahkan security headers pada setiap response:
- `X-Gateway: ATMA-API-Gateway`
- `X-Gateway-Version: 1.0.0`
- `X-Request-ID: <unique-request-id>`

## 📊 Rate Limiting Headers

Ketika rate limit diterapkan, response akan menyertakan headers:
- `X-RateLimit-Limit: <limit>`
- `X-RateLimit-Remaining: <remaining>`
- `X-RateLimit-Reset: <reset-time>`

## 🌐 CORS Configuration

API Gateway dikonfigurasi untuk menerima request dari:
- `http://localhost:3000` (Frontend development)
- `http://localhost:5173` (Vite development server)
- Production domains (sesuai konfigurasi)

## 📝 Request/Response Examples

### Successful Response Format
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Error description",
  "details": { /* additional error details */ }
}
```

### Pagination Format
```json
{
  "success": true,
  "data": {
    "items": [ /* array of items */ ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```
